// @ts-nocheck

const path = require('path');
const fs = require('fs');
const pdf = require('pdf-parse');
const assert = require('assert');

const csv = require('csv-parser');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;

const updateInvoiceCsv = async (csvFile: string, invoicesFolder: string) => {
  const invoiceNumbers = await getInvoiceNumbers(invoicesFolder);
  const csvFilePath = "/Users/<USER>/projects/aleannlab/aleannlabV2/slack/all.csv";
  const missedInvoicesFolder = '/Users/<USER>/Downloads/invoice_missed';

  // Read the CSV file to get the list of invoice numbers
  const listedInvoiceNumbers = new Set();
  fs.createReadStream(csvFilePath)
    .pipe(csv())
    .on('data', (row: any) => {
      listedInvoiceNumbers.add(row['Nr dokumentu']);
    })
    .on('end', () => {
      // Check and copy invoices not listed in the CSV file
      for (const [key, { invoiceNumber }] of Object.entries(invoiceNumbers)) {
        if (!listedInvoiceNumbers.has(invoiceNumber)) {
          console.log(`Invoice not listed in CSV: ${invoiceNumber}`);
          const invoiceFilePath = path.join(invoicesFolder, `T${key}.pdf`);
          const destinationPath = path.join(missedInvoicesFolder, `${key}.pdf`);
          fs.copyFile(invoiceFilePath, destinationPath, (err) => {
            if (err) {
              console.error(`Error copying file for invoice ${invoiceNumber}:`, err);
            } else {
              console.log(`Copied invoice ${invoiceNumber} to missed invoices folder.`);
            }
          });
        }
      }
    });
  console.log(invoiceNumbers);
  return ;
  let headers: any = [];
  let data: any = [];
  let unlistedInvoices: any = {...invoiceNumbers}; // Copy of invoiceNumbers to track unlisted invoices

  fs.createReadStream(csvFile)
    .pipe(csv())
    .on('headers', (headerList: any) => {
      headers = headerList.map((header: any) => ({ id: header, title: header }));
      headers.push({ id: 'Invoice #', title: 'Invoice #' });
      headers.push({ id: 'Country', title: 'Country' });
      data.push(headers); // Add headers as first line of data
    })
    .on('data', (row: any) => {
      const refId = invoiceNumbers[row['Ref ID']];
      if (refId) {
        const { invoiceNumber, country } = refId;
        row['Invoice #'] = invoiceNumber;
        row['Country'] = country;
        delete unlistedInvoices[row['Ref ID']]; // Remove listed invoice from unlistedInvoices
      }
      data.push(row);
    })
    .on('end', async () => {
      const csvWriter = createCsvWriter({
        path: './invoice_updated.csv',
        header: headers,
        append: false // Change append to false to overwrite existing file
      });
      await csvWriter.writeRecords(data);
      console.log('CSV file updated.');
      console.log('Unlisted Invoices:', unlistedInvoices); // Print unlisted invoices
    });
}




const getInvoiceNumbers = async (invoicesFolder: string): Promise<{ [key: string]: { invoiceNumber: string, country: string } }> => {
  const directoryPath = path.join(invoicesFolder);
  const files = fs.readdirSync(directoryPath);
  const invoiceMapping: { [key: string]: { invoiceNumber: string, country: string } } = {};

  for (const file of files) {
    if (file === ".DS_Store") {
      continue
    }
   try {
     const text = await extractText(path.join(directoryPath, file));
    //  const invoiceNumber = text.match(/INVOICE (#T\d{9})/)[1];
     const invoiceNumber = text.match(/INVOICE (#T\d{9})/)[1];
     let country: string = '';
     if (text.includes('Poland')) {
       country = 'Poland';
     } else if (text.includes('Ukraine')) {
       country = 'Ukraine';
     }
     const fileNameWithoutExtension = path.basename(file, path.extname(file)).substring(1);
     invoiceMapping[fileNameWithoutExtension] = { invoiceNumber: invoiceNumber.replace('#', ''), country };
   } catch (e) {
     console.log(e); // 404
   }
  }

  return invoiceMapping;
}

const extractText = async (pathStr: any) => {
  assert (fs.existsSync(pathStr), `Path does not exist ${pathStr}`)
  const pdfFile = path.resolve(pathStr)
  const dataBuffer = fs.readFileSync(pdfFile);
  const data = await pdf(dataBuffer)
  // console.log(data.text);
  return data.text
}

// extractText("./T638000121.pdf")


const copyFilesByRefId = async (csvFilePath: string, sourceDirectory: string, targetDirectory: string): Promise<void> => {
  const refIds: Set<string> = new Set();
  let csvItemCount = 0;

  // Create target directory if it doesn't exist
  if (!fs.existsSync(targetDirectory)) {
    fs.mkdirSync(targetDirectory);
  }

  fs.createReadStream(csvFilePath)
    .pipe(csv())
    .on('data', (row: any) => {
      refIds.add(row['Ref ID'].toString());
      csvItemCount++;
    })
    .on('end', () => {
      const files = fs.readdirSync(sourceDirectory);
      let count = 0;
      const filesNames = []
      for (const file of files) {
        const fileNameWithoutExtension = path.basename(file, path.extname(file)).substring(1);
        if (refIds.has(fileNameWithoutExtension)) {
          count++;
          filesNames.push(fileNameWithoutExtension);
          const sourceFile = path.join(sourceDirectory, file);
          const targetFile = path.join(targetDirectory, file);
          fs.copyFileSync(sourceFile, targetFile);
        } else {
          console.log("???");
        }
      }

      for (let ref of refIds) {
        if (!filesNames.includes(ref)) {
          console.log(ref);
        }
      }
      // filesNames
      console.log('Files copied successfully. Total copied files:', count);
      console.log('Total items in CSV file:', csvItemCount);
    });
}


const csvFile = '/Users/<USER>/projects/aleannlab/aleannlabV2/slack/transaction_report.csv'
const invoicePath = '/Users/<USER>/Downloads/07_09_2025-bulk-invoice-95c6341deadcf7eb-SEA'
copyFilesByRefId(
  csvFile,
  invoicePath,
  './invoices'
).then(() => {
  console.log('Operation completed.');
});

updateInvoiceCsv(csvFile, invoicePath).then((res) => {
  console.log(res);
})
