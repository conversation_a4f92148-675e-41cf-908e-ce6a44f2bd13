{"name": "slack-upwork-manager", "version": "1.0.0", "main": "index.js", "license": "MIT", "workspaces": ["frontend"], "dependencies": {"@google-cloud/local-auth": "^2.1.0", "@notionhq/client": "^2.2.13", "@radix-ui/react-switch": "^1.2.5", "@sendgrid/mail": "^8.1.3", "@supabase/supabase-js": "^2.49.10", "@types/pdf-parse": "^1.1.4", "@upwork/node-upwork-oauth2": "^2.3.0", "amo-html-to-mrkdwn": "^3.4.1", "aws-sdk": "^2.1274.0", "axios": "^1.8.1", "body-parser": "^1.20.2", "cheerio": "^1.0.0-rc.12", "csv-parse": "^5.3.2", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.0.1", "dotenv-flow": "^3.2.0", "express": "^4.18.2", "fastq": "^1.15.0", "fs-extra": "^11.3.0", "google-spreadsheet": "^4.1.0", "googleapis": "^105.0.0", "gpt-3-encoder": "^1.1.4", "graphql": "^16.10.0", "graphql-tag": "^2.12.6", "langchain": "^0.0.187", "moment": "^2.29.4", "nedb": "^1.8.0", "node-cache": "^5.1.2", "node-upwork-oauth2": "^0.0.1-security", "openai": "^3.2.1", "path": "^0.12.7", "pdf-parse": "^1.1.1", "pdf2json": "^3.0.5", "puppeteer": "^24.4.0", "rss-feed-emitter": "^3.2.3", "simple-json-db": "^2.0.0", "sqlite3": "^5.1.2", "ts-node": "^10.9.1", "typescript": "^4.9.5", "upwork-api": "^1.3.8"}, "scripts": {"start": "ts-node --transpile-only index.ts", "invoices": "ts-node --transpile-only invoices.ts", "server": "ts-node server.ts", "tenis": "ts-node tennis.ts", "test": "node test.js", "frontend:dev": "cd frontend && pnpm dev", "frontend:build": "cd frontend && pnpm build", "frontend:install": "cd frontend && pnpm install", "setup": "pnpm install && pnpm frontend:install"}}